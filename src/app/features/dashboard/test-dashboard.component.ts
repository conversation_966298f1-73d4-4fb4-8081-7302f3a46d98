import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-test-dashboard',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div style="padding: 24px; background: #f7f9fa; min-height: 100vh;">
      <h1 style="color: #1976d2; font-size: 2rem; margin-bottom: 16px;">
        🎉 Enhanced StaffManager Dashboard is Working!
      </h1>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 24px;">
        <mat-card style="padding: 16px;">
          <mat-card-header>
            <mat-card-title>
              <mat-icon style="margin-right: 8px; color: #1976d2;">dashboard</mat-icon>
              New Features Working
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <p>✅ Angular 19 with standalone components</p>
            <p>✅ Enhanced routing system</p>
            <p>✅ Material Design UI</p>
            <p>✅ Custom theme system ready</p>
          </mat-card-content>
        </mat-card>

        <mat-card style="padding: 16px;">
          <mat-card-header>
            <mat-card-title>
              <mat-icon style="margin-right: 8px; color: #9c27b0;">people</mat-icon>
              Staff Management
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <p>✅ Staff models and services created</p>
            <p>✅ Staff list component ready</p>
            <p>✅ Advanced filtering system</p>
            <p>✅ Employee portal foundation</p>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary" onclick="window.location.href='/staff'">
              View Staff Directory
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card style="padding: 16px;">
          <mat-card-header>
            <mat-card-title>
              <mat-icon style="margin-right: 8px; color: #ff6b35;">calendar_today</mat-icon>
              Navigation Test
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <p>Test the navigation menu:</p>
            <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-top: 12px;">
              <button mat-stroked-button onclick="window.location.href='/dashboard'">Dashboard</button>
              <button mat-stroked-button onclick="window.location.href='/staff'">Staff</button>
              <button mat-stroked-button onclick="window.location.href='/calendar'">Calendar</button>
              <button mat-stroked-button onclick="window.location.href='/tasks'">Tasks</button>
              <button mat-stroked-button onclick="window.location.href='/settings'">Settings</button>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card style="padding: 16px;">
          <mat-card-header>
            <mat-card-title>
              <mat-icon style="margin-right: 8px; color: #4caf50;">check_circle</mat-icon>
              Migration Status
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <p><strong>Phase 1 Complete:</strong></p>
            <p>✅ Project structure migrated</p>
            <p>✅ Theme system implemented</p>
            <p>✅ Navigation working</p>
            <p>✅ Staff management foundation</p>
            <p>🔄 Enhanced dashboard (in progress)</p>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-card style="padding: 16px; background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);">
        <mat-card-content>
          <h2 style="margin-top: 0; color: #1565c0;">
            <mat-icon style="margin-right: 8px;">info</mat-icon>
            What You Should See
          </h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
            <div>
              <h3>✅ Current Working Features:</h3>
              <ul>
                <li>Responsive sidebar navigation</li>
                <li>Material Design components</li>
                <li>Route-based navigation</li>
                <li>Professional styling</li>
                <li>Staff directory (click Staff menu)</li>
              </ul>
            </div>
            <div>
              <h3>🔄 Coming Next:</h3>
              <ul>
                <li>Draggable dashboard widgets</li>
                <li>Dark/light theme toggle</li>
                <li>Calendar integration</li>
                <li>Task management</li>
                <li>Staff forms and CRUD</li>
              </ul>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    mat-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    mat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.12);
    }
    mat-card-title {
      display: flex;
      align-items: center;
    }
  `]
})
export class TestDashboardComponent {}
